#!/bin/bash
# 保存此文件为 fix_cleanclip.sh
# 使用前运行 chmod +x fix_cleanclip.sh 使其可执行

# 修改UserDefaults设置
defaults write com.antiless.cleanclip.mac expirationDate "2099-12-31 23:59:59 +0000"
defaults write com.antiless.cleanclip.mac planType "permanent"
defaults write com.antiless.cleanclip.mac historySize "-1"

# 检查hosts文件是否包含我们的修改
if ! grep -q "clip-purchase.macaify.com" /etc/hosts; then
  echo "需要修改hosts文件。请手动运行："
  echo "sudo sh -c 'echo \"127.0.0.1 clip-purchase.macaify.com\" >> /etc/hosts'"
else
  echo "hosts文件已包含我们的修改。"
fi

echo "完成！"