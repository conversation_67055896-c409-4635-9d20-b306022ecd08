<?php

class DoubaoChat
{
    private string $apiUrl = 'https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7498609401906873897&device_platform=web&language=zh&pc_version=2.20.0&pkg_type=release_version&real_aid=497858&region=CN&samantha_web=1&sys_region=CN&tea_uuid=7498609418327655973&use-olympus-account=1&version_code=20800&web_id=7498609418327655973&msToken=Qp0gjN06I6jrKTy9yomgyHVdKq-RNiLEOhob6yL_uiLvLauNtuv-uZegdIkHR_5XbIh4F4xNnYwpgG3wJqMEuS3N3xPaodF2uVgHvu0_s9sddLtnPSGujcn7DzbeJRhmcUEIcrJxNnnvSBubV48899I2I9acmF2nYZ-Mk_UyXiFGiaAfCaP8qYA%3D&a_bogus=Ev4jke6LdZAcPplbYKsESAd572AArsuygFiKWnqRSKOFT1Fcv51JxP8cGKYTov5yaWshiH-7adY9YjdG%2FY-R6McpzmhkSLvbt45cVhvL2qi6GUGmLHmNe0zFow0FUmiwe%2F9WNAXRWsMnIndAnN5mWd-G75z95cEdRqZSD%2Fw9SEAXfCSki930O9DpPgTqij%3D%3D';
    
    private array $headers = [
        'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept-Encoding: gzip, deflate, br, zstd',
        'Content-Type: application/json',
        'x-flow-trace: 04-001fc4c82c8c2f960005cbea9e789033-0015336fd7306e61-01',
        'sec-ch-ua-platform: "macOS"',
        'sec-ch-ua: "Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        'sec-ch-ua-mobile: ?0',
        'agw-js-conv: str',
        'last-event-id: undefined',
        'origin: https://www.doubao.com',
        'sec-fetch-site: same-origin',
        'sec-fetch-mode: cors',
        'sec-fetch-dest: empty',
        'referer: https://www.doubao.com/chat/',
        'accept-language: zh-CN,zh;q=0.9',
        'priority: u=1, i',
        'Cookie: _ga=GA1.1.1685130387.1745906075; i18next=zh; oauth_token=82ae6907-c3a8-4e51-9ec2-4bcd7c87602a; s_v_web_id=verify_maowfw5c_zho4v1dL_N2XB_4ZAX_BeYN_ZgUl2vL3ieYO; passport_csrf_token=4514851098d3ad0e0e68cdb9f4a87a1f; passport_csrf_token_default=4514851098d3ad0e0e68cdb9f4a87a1f; odin_tt=cee9ea3658c478c53d20e83ecf0e09a78c763bfa1f8fb10626b95d026c234078ef9a218b7597f8deee69b37a413c2e04871eebaffabf57b6db145f9475c69484; n_mh=NZ66y_3jbT04s2gefO-EMKEB6EsWtjVtv18mMDhVYNc; passport_auth_status=cfe679114e4355fc09c3ef9715977845%2C; passport_auth_status_ss=cfe679114e4355fc09c3ef9715977845%2C; sid_guard=bacddcb63cf932db68f3d1c6bf118a98%7C1747285111%7C5184000%7CMon%2C+14-Jul-2025+04%3A58%3A31+GMT; uid_tt=43d8eefbb5c08de2797003e8d1218619; uid_tt_ss=43d8eefbb5c08de2797003e8d1218619; sid_tt=bacddcb63cf932db68f3d1c6bf118a98; sessionid=bacddcb63cf932db68f3d1c6bf118a98; sessionid_ss=bacddcb63cf932db68f3d1c6bf118a98; is_staff_user=false; sid_ucp_v1=1.0.0-KGFmYTA0NzhiMzBiZGViMmM4MmRkMWExN2VlM2IxMGQzMmFmMmQ4ZjIKHgiNuYDi3814EPfolcEGGMKxHiAMMM2J_6YGOAhAJhoCbGYiIGJhY2RkY2I2M2NmOTMyZGI2OGYzZDFjNmJmMTE4YTk4; ssid_ucp_v1=1.0.0-KGFmYTA0NzhiMzBiZGViMmM4MmRkMWExN2VlM2IxMGQzMmFmMmQ4ZjIKHgiNuYDi3814EPfolcEGGMKxHiAMMM2J_6YGOAhAJhoCbGYiIGJhY2RkY2I2M2NmOTMyZGI2OGYzZDFjNmJmMTE4YTk4; flow_ssr_sidebar_expand=1; gd_random=eyJtYXRjaCI6ZmFsc2UsInBlcmNlbnQiOjAuNDQ2MTc2NDEzODQxMjUxNH0=.sJtokZ6SPstytAPTZhZUQBch6qHcvSCzLfelAi72yJ0=; ttwid=1%7C-W9rGbA7LP2QhQOx2PCDPAfZyy-GCB3Pg2Ti2aFUi50%7C1748422536%7Ce12b1b5abe6378415c0091dcb79f384f84655d18672c3f4900e134ab8f951335; msToken=aGrcXyabS_Btw8FTHColpeCmxm867h3lXVqqf58xHAR1UAkfXZdAaRxlDoMlXoj2jlJmnSNNBFNFch7czm2d3iCZWXTQAq-XfXZZ2rkb9I47Q5PSq1P896_eXuTFU6TxYFSaqpwf0WC3ag==; passport_fe_beating_status=true; _ga_G8EP5CG8VZ=GS2.1.s1748422070$o27$g1$t1748422543$j16$l0$h0; tt_scid=Kh0tNBbeGOhOFm0EhmVYOm87RcFvCwunqGY-gRFmmrvW62LprYWOCSVhZLiglq-te63a'
    ];

    private array $results = [];
    private string $currentBuffer = '';

    private function generateUuidV4(): string
    {
        $data = random_bytes(16);
        assert(strlen($data) == 16);

        $data[6] = chr(ord($data[6]) & 0x0f | 0x40);
        $data[8] = chr(ord($data[8]) & 0x3f | 0x80);

        return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
    }

    private function buildPayload(string $prompt, string $localMessageId): string
    {
        $messageContent = [
            'text' => $prompt,
            'content_type' => 2009,
            'attachments' => []
        ];

        $payload = [
            'messages' => [
                [
                    'content' => json_encode($messageContent, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                    'content_type' => 2009,
                    'attachments' => []
                ]
            ],
            'completion_option' => [
                'is_regen' => false,
                'with_suggest' => false,
                'need_create_conversation' => true,
                'launch_stage' => 1,
                'is_replace' => false,
                'is_delete' => false,
                'message_from' => 0,
                'use_auto_cot' => false,
                'resend_for_regen' => false,
                'event_id' => "0"
            ],
            'conversation_id' => "0",
            'local_conversation_id' => "local_5140226371168141",
            'local_message_id' => $localMessageId
        ];
        return json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }

    private function processStreamChunk(string $chunk): void
    {
        $this->currentBuffer .= $chunk;

        while (($newlinePos = strpos($this->currentBuffer, "\n")) !== false) {
            $line = substr($this->currentBuffer, 0, $newlinePos);
            $this->currentBuffer = substr($this->currentBuffer, $newlinePos + 1);

            $line = trim($line);


            if (empty($line)) {
                continue;
            }

            if (strpos($line, 'data: ') === 0) {
                $jsonDataString = substr($line, strlen('data: '));
                $eventData = json_decode($jsonDataString, true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    continue;
                }
                
                if (isset($eventData['event_data']) && is_string($eventData['event_data'])) {
                    $innerJsonString = $eventData['event_data'];

                    if (strpos($innerJsonString, '\"prompt\"') !== false) {
                        $messageContainer = json_decode($innerJsonString, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            continue;
                        }

                        if (isset($messageContainer['message']['content']) && is_string($messageContainer['message']['content'])) {
                            $creationsJsonString = $messageContainer['message']['content'];
                            $creationsData = json_decode($creationsJsonString, true);

                            if (json_last_error() !== JSON_ERROR_NONE) {
                                continue;
                            }


                            if (isset($creationsData['creations']) && is_array($creationsData['creations'])) {
                                foreach ($creationsData['creations'] as $creation) {
                                    $imageUrl = $creation['image']['image_ori']['url'] ?? null;
                                    $genPrompt = $creation['image']['gen_params']['prompt'] ?? null;

                                    if ($imageUrl && $genPrompt) {
                                        $this->results['images'][] = [
                                            'image_url' => $imageUrl,
                                            'prompt' => $genPrompt
                                        ];
                                    }
                                }
                            }
                        }
                    } else if (strpos($innerJsonString, "tts_content") !== false) {
                        $creationsData = json_decode($innerJsonString, true);

                        if (json_last_error() !== JSON_ERROR_NONE) {
                            continue;
                        }

                        if (isset($creationsData['tts_content']) && is_string($creationsData['tts_content'])) {
                            $this->results['tts_content'] = $creationsData['tts_content'];
                        }
                    }
                }
            }
        }
    }
    
    private function handleStreamData($curl, string $data): int
    {
        $this->processStreamChunk($data);
        return strlen($data);
    }

    public function getImagesAndPrompts(string $prompt): array
    {
        $this->results = [
            'tts_content' => "",
            'images' => []
        ];
        $this->currentBuffer = '';
        $localMessageId = $this->generateUuidV4();
        $payloadString = $this->buildPayload($prompt, $localMessageId);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payloadString);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $this->headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, [$this, 'handleStreamData']);
        curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);


        $success = curl_exec($ch);

        if ($success === false) {
            
        }
        
        if (!empty($this->currentBuffer)) {
            $this->processStreamChunk("\n"); 
        }

        curl_close($ch);
        return $this->results;
    }
}

header('Content-Type: application/json; charset=utf-8');

$doubao = new DoubaoChat();
$userPrompt = "像素插画风格人物";
$results = $doubao->getImagesAndPrompts($userPrompt);

echo json_encode($results, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);


?>