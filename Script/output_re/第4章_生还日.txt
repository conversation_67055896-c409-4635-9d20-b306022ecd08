【分镜生成指令模板】
请按照以下结构解析我提供的小说章节内容：
场景解构

识别场景转换节点（环境/时间/空间变化）

提取5-7个核心场景段落

标注每个场景的：

时空坐标（季节/时辰/地理坐标）

环境特征（光影/气味/温度/特殊物件）

空间动态（移动路径/交互物体）
人物特写

标注每场景中出场人物

生成人物画像卡：

   [角色名]：
视觉特征（动态外貌/服饰纹理/微表情）

行为模式（习惯动作/微反应/肢体语言）

情绪光谱（基础情绪+3层递进变化）
动态刻画

分解关键动作序列（至少三级动作细化），必须使用[]包裹起来

例：
[拔剑动作] 
1级：右手握剑 
2级：拇指顶开剑鞘三寸 
3级：手腕内旋15度触发机关
捕捉感官细节：

听觉层次（环境音/人声/物体声响）

触觉反馈（材质接触/力道变化）

空间共振（动作引发的环境反馈）
分镜整合

按照故事进程生成编号分镜，每个分镜包含：
[镜号]：[起止时间节点]
场景定位：[空间坐标]+[环境状态]
环境特征：[环境特征]+[场景丰富]
人物状态：[角色名]情绪值（0-100）+核心动作
镜头语言：[景别选择]+[运镜方式]+[光影强调点]
关键帧描述：[（200字内具象化描写，需包含3个以上动态细节）]

【示例】
输入文本：
"客栈二楼突然传来瓷器碎裂声，青衣书生撞开雕花木窗纵身跃下，腰间玉佩在月光中划出青虹轨迹"

解析输出：
[镜号17]：[子时三刻-子时四刻]
场景定位：悦来客栈后院（桂花树/青石板地/西偏月）
环境特征：[环境特征]+[场景丰富]
人物状态：陆子卿（惊怒值82）破窗逃生
镜头语言：中景跟拍+俯冲式下降+玉佩反光追踪
关键帧描述：[窗棂木屑飞溅瞬间，陆子卿左肩布料被窗框钩出三寸裂口。下坠过程中右手本能护住头顶发冠，束发丝带被气流掀起与下裳形成45度夹角。玉佩链锁在急坠中绷成直线，青玉表面的螭纹在月光下投射出波浪状阴影，随身体旋转在地面投射出螺旋光斑。]

请提供需要解析的小说章节内容，我将按照此框架生成完整分镜脚本。